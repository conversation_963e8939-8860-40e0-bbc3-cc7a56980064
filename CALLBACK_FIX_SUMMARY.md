# Callback Fix Summary - /createbot Strategy Selection

## 🐛 Vấn đề
Khi sử dụng command `/createbot` và click vào các callback button để chọn strategy (Conservative, Aggressive, etc.), người dùng gặp lỗi:
```
❌ Builder callback không hợp lệ: 'strategy_conservative_long'
```

## 🔍 Nguyên nhân
Callback `strategy_conservative_long` và các callback strategy khác được tạo trong template của createbot wizard, nhưng bị route sai đến `strategy_builder_handler` thay vì `wizard_handler` trong logic routing của `main_handler.py`.

### Logic routing cũ (có lỗi):
```python
elif data.startswith("strategy_") or data.startswith("step_") or ...:
    await self._handle_builder_callback(query, data)  # SAI!
```

## ✅ Giải pháp
Đã sửa logic routing trong `src/infrastructure/telegram/handlers/main_handler.py` để:

1. **Route callback strategy selection từ createbot đến wizard handler:**
   - `strategy_conservative_long`
   - `strategy_aggressive_long` 
   - `strategy_balanced_long_short`
   - `strategy_scalping`
   - `strategy_swing_trading`
   - `strategy_custom_*`
   - `create_new_strategy`
   - `strategy_compare`

2. **Giữ nguyên routing cho strategy builder callbacks:**
   - `builder_*`
   - `template_*`
   - `my_strategies`
   - `delete_strategy_*`
   - `step_*`
   - `entry_*`, `dca_*`, `exit_*`, `tp_*`, `sl_*`

### Logic routing mới (đã sửa):
```python
elif (data.startswith("strategy_") and (
    data.startswith("strategy_conservative") or 
    data.startswith("strategy_aggressive") or 
    data.startswith("strategy_balanced") or 
    data.startswith("strategy_scalping") or 
    data.startswith("strategy_swing") or 
    data.startswith("strategy_custom")
)) or data == "create_new_strategy" or data == "strategy_compare":
    # Route strategy selection callbacks from createbot to wizard handler
    await self._handle_createbot_callback(query, data)
elif data.startswith("builder_") or data.startswith("template_") or ...:
    # Route to strategy builder handler
    await self._handle_builder_callback(query, data)
```

## 🔧 Thay đổi chi tiết

### 1. `src/infrastructure/telegram/handlers/main_handler.py`
- **Dòng 711-717**: Sửa logic routing callback để phân biệt giữa createbot strategy selection và strategy builder callbacks

### 2. `src/infrastructure/telegram/handlers/wizard_handler.py`
- **Dòng 580-589**: Thêm xử lý cho callback `strategy_compare` trong wizard handler

## 🧪 Testing
Đã tạo và chạy test script `test_routing_logic.py` để verify logic routing:
- ✅ 24/24 test cases passed
- ✅ Strategy selection callbacks route đến wizard handler
- ✅ Strategy builder callbacks vẫn route đến builder handler
- ✅ Các callback khác route đến đúng handlers

## 🚀 Kết quả
Sau khi áp dụng fix:
1. ✅ Callback buttons trong `/createbot` hoạt động bình thường
2. ✅ Strategy builder (`/createstrategy`) vẫn hoạt động bình thường  
3. ✅ Không có breaking changes cho các features khác

## 📋 Cách test
1. Chạy `/createbot` trong Telegram
2. Click vào các strategy buttons (Conservative, Aggressive, etc.)
3. Verify không còn lỗi "Builder callback không hợp lệ"
4. Verify strategy selection hoạt động đúng
5. Test `/createstrategy` để đảm bảo strategy builder vẫn hoạt động

## 🔄 Container restart
Đã restart Telegram bot container để áp dụng changes:
```bash
docker restart telegramusdt
```

## 📝 Notes
- Fix này chỉ ảnh hưởng đến callback routing logic
- Không thay đổi business logic của wizard hoặc strategy builder
- Backward compatible với tất cả existing features
