#!/usr/bin/env python3
"""Main Telegram Command Handler with clean modular architecture."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

# Import modular handlers
from .base_handler import BaseTelegramHandler
from .credential_handler import CredentialHandler
from .bot_management_handler import BotManagementHandler
from .wizard_handler import WizardHandler
from .assets_handler import AssetsHandler
from .strategy_builder_handler import StrategyBuilderHandler

from ..templates import TelegramTemplates
from ..auth import TelegramAuthService, require_auth, require_auth_callback


class MainTelegramHandler(BaseTelegramHandler):
    """Main Telegram command handler with clean modular architecture."""

    def __init__(self, bot_token: str):
        super().__init__(bot_token)

        # Initialize authorization service
        self.auth_service = TelegramAuthService()

        # Initialize specialized handlers with error handling
        # Share the same session manager across all handlers
        try:
            print("   - Initializing CredentialHandler...")
            self.credential_handler = CredentialHandler(bot_token)
            self.credential_handler.session_manager = self.session_manager  # Share session manager
            self.credential_handler.auth_service = self.auth_service  # Share auth service

            print("   - Initializing BotManagementHandler...")
            self.bot_management_handler = BotManagementHandler(bot_token)
            self.bot_management_handler.session_manager = self.session_manager  # Share session manager
            self.bot_management_handler.auth_service = self.auth_service  # Share auth service

            print("   - Initializing WizardHandler...")
            self.wizard_handler = WizardHandler(bot_token)
            self.wizard_handler.session_manager = self.session_manager  # Share session manager
            self.wizard_handler.auth_service = self.auth_service  # Share auth service

            print("   - Initializing AssetsHandler...")
            self.assets_handler = AssetsHandler(bot_token)
            self.assets_handler.session_manager = self.session_manager  # Share session manager
            self.assets_handler.auth_service = self.auth_service  # Share auth service

            print("   - Initializing StrategyBuilderHandler...")
            self.strategy_builder_handler = StrategyBuilderHandler(bot_token)
            self.strategy_builder_handler.session_manager = self.session_manager  # Share session manager
            self.strategy_builder_handler.auth_service = self.auth_service  # Share auth service

            print("   - Initializing SelfTestHandler...")
            from src.infrastructure.telegram.handlers.self_test_handler import SelfTestHandler
            self.self_test_handler = SelfTestHandler(bot_token, self.auth_service)

            print("   - Initializing AutoTestHandler...")
            from src.infrastructure.telegram.handlers.auto_test_handler import AutoTestHandler
            self.auto_test_handler = AutoTestHandler(bot_token, self.auth_service)

            print("   - All handlers initialized successfully")
        except Exception as e:
            print(f"   ❌ Error initializing handlers: {e}")
            self.logger.error(f"Error initializing handlers: {e}", exc_info=True)
            # Create dummy handlers to prevent crashes
            self.credential_handler = None
            self.bot_management_handler = None
            self.wizard_handler = None
            self.assets_handler = None
            self.strategy_builder_handler = None
            self.self_test_handler = None
            self.auto_test_handler = None
    
    def start(self):
        """Start the Telegram bot with polling"""
        try:
            print("🔧 Creating Telegram application...")
            self.application = Application.builder().token(self.bot_token).build()

            print("📝 Adding command handlers...")
            self._setup_handlers()

            print("🚀 Starting bot...")
            print(f"   Application: {self.application}")
            print(f"   Handlers registered: {len(self.application.handlers)}")

            # Debug handler details
            for group_id, handler_group in self.application.handlers.items():
                print(f"   Handler group {group_id}: {len(handler_group)} handlers")
                for j, handler in enumerate(handler_group):
                    print(f"     Handler {j}: {type(handler).__name__}")

            # Add debug for polling
            print("   Starting polling...")
            print(f"   Drop pending updates: False")
            print(f"   Allowed updates: ['message', 'callback_query']")

            # Add custom error handler with enhanced parsing error handling
            async def error_handler(update, context):
                error_msg = str(context.error)
                print(f"🚨 ERROR: {error_msg}")

                # Log detailed debug info for parsing errors
                if "can't parse entities" in error_msg.lower():
                    print(f"🔍 DEBUG: Received update: {update.update_id}")
                    print(f"   Update type: {type(update)}")
                    print(f"   Update dict: {update.to_dict()}")

                    if update.message:
                        print(f"   Message from user {update.effective_user.id}: {update.message.text}")
                        print(f"   Chat ID: {update.effective_chat.id}")
                        print(f"   Message ID: {update.message.message_id}")

                    self.logger.info(f"Debug update: {update.update_id} from user {update.effective_user.id}")

                self.logger.error(f"Update {update} caused error {context.error}")

            self.application.add_error_handler(error_handler)

            # Add debug for all updates received
            async def debug_all_updates(update, context):
                print(f"🔄 POLLING DEBUG: Received update {update.update_id}")
                print(f"   Update type: {type(update)}")
                if update.message:
                    print(f"   Message: {update.message.text}")
                    print(f"   From user: {update.effective_user.id}")
                    print(f"   Chat: {update.effective_chat.id}")
                if update.callback_query:
                    print(f"   Callback query: {update.callback_query.data}")
                # Continue processing
                return

            # Add this handler with highest priority
            from telegram.ext import TypeHandler
            from telegram import Update
            self.application.add_handler(TypeHandler(Update, debug_all_updates), group=-1)

            # Also add a simple message handler for debugging
            async def simple_debug(update, context):
                print(f"🔍 SIMPLE DEBUG: Got update {update.update_id}")
                if update.message:
                    print(f"   Text: {update.message.text}")
                return

            self.application.add_handler(MessageHandler(filters.ALL, simple_debug), group=-1)

            # Add debug for application events
            async def post_init(application):
                print("🚀 Application post_init called")

            async def post_shutdown(application):
                print("🛑 Application post_shutdown called")

            self.application.post_init = post_init
            self.application.post_shutdown = post_shutdown

            print("   Calling run_polling...")

            # Add custom update processor
            async def process_update(update, context):
                print(f"🔄 CUSTOM: Processing update {update.update_id}")
                if update.message:
                    print(f"   Message: '{update.message.text}'")
                    print(f"   From: {update.effective_user.id}")
                    print(f"   Chat: {update.effective_chat.id}")
                # Continue with normal processing
                return

            # Add the custom processor
            from telegram.ext import TypeHandler
            from telegram import Update
            self.application.add_handler(TypeHandler(Update, process_update), group=-2)

            self.application.run_polling(
                drop_pending_updates=True,
                allowed_updates=["message", "callback_query"],
                poll_interval=1.0,
                timeout=10
            )
            print("   run_polling completed")

        except Exception as e:
            self.logger.error(f"Error starting bot: {e}", exc_info=True)
            raise
    
    def _setup_handlers(self):
        """Setup all command handlers"""
        print("📝 Setting up command handlers...")

        # Basic commands
        print("   - Adding /start handler")
        self.application.add_handler(CommandHandler("start", self.handle_start))
        print("   - Adding /help handler")
        self.application.add_handler(CommandHandler("help", self.handle_help))
        print("   - Adding /cancel handler")
        if self.wizard_handler:
            self.application.add_handler(CommandHandler("cancel", self.wizard_handler.handle_cancel))
        else:
            print("   ⚠️ Wizard handler not available for cancel")

        # User management commands (admin only)
        print("   - Adding user management handlers")
        self.application.add_handler(CommandHandler("adduser", self.handle_add_user))
        self.application.add_handler(CommandHandler("removeuser", self.handle_remove_user))
        self.application.add_handler(CommandHandler("listusers", self.handle_list_users))
        self.application.add_handler(CommandHandler("myinfo", self.handle_my_info))

        # Add test handler
        print("   - Adding /test handler")
        self.application.add_handler(CommandHandler("test", self.handle_test))

        # Add self-test command
        print("   - Adding /selftest handler")
        self.application.add_handler(CommandHandler("selftest", self.handle_selftest))

        # Add auto-test command
        print("   - Adding /autotest handler")
        self.application.add_handler(CommandHandler("autotest", self.handle_autotest))

        # Add simulate command
        print("   - Adding /simulate handler")
        self.application.add_handler(CommandHandler("simulate", self.handle_simulate))

        # Add testcmd command
        print("   - Adding /testcmd handler")
        self.application.add_handler(CommandHandler("testcmd", self.handle_testcmd))

        # Credential commands
        if self.credential_handler:
            print("   - Adding credential handlers")
            self.application.add_handler(CommandHandler("addcreds", self.credential_handler.handle_addcreds_wizard))
            self.application.add_handler(CommandHandler("listcreds", self.credential_handler.handle_listcreds))
            self.application.add_handler(CommandHandler("showcreds", self.credential_handler.handle_showcreds))
            self.application.add_handler(CommandHandler("deletecreds", self.credential_handler.handle_deletecreds))
        else:
            print("   ⚠️ Credential handler not available")

        # Assets commands
        if self.assets_handler:
            print("   - Adding assets handler")
            self.application.add_handler(CommandHandler("assets", self.assets_handler.handle_assets))
        else:
            print("   ⚠️ Assets handler not available")

        # Bot management commands
        if self.bot_management_handler and self.wizard_handler:
            print("   - Adding bot management handlers")
            self.application.add_handler(CommandHandler("createbot", self.wizard_handler.handle_createbot_wizard))
            self.application.add_handler(CommandHandler("startbot", self.bot_management_handler.handle_start_bot))
            self.application.add_handler(CommandHandler("list", self.bot_management_handler.handle_list_bots))
            self.application.add_handler(CommandHandler("profiles", self.bot_management_handler.handle_profiles))
            self.application.add_handler(CommandHandler("status", self.bot_management_handler.handle_status_bot))
            self.application.add_handler(CommandHandler("stop", self.bot_management_handler.handle_stop_bot))
            self.application.add_handler(CommandHandler("stopall", self.bot_management_handler.handle_stop_all_bots))
            self.application.add_handler(CommandHandler("logs", self.bot_management_handler.handle_logs_bot))
            self.application.add_handler(CommandHandler("restart", self.bot_management_handler.handle_restart_bot))
            self.application.add_handler(CommandHandler("strategies", self.bot_management_handler.handle_strategies))
        else:
            print("   ⚠️ Bot management handlers not available")

        # Strategy builder commands
        if self.strategy_builder_handler:
            print("   - Adding strategy builder handlers")
            self.application.add_handler(CommandHandler("createstrategy", self.strategy_builder_handler.handle_createstrategy))
            self.application.add_handler(CommandHandler("mystrategies", self.strategy_builder_handler.handle_mystrategies))
            self.application.add_handler(CommandHandler("deletestrategy", self.strategy_builder_handler.handle_deletestrategy))
            self.application.add_handler(CommandHandler("duplicatestrategy", self.strategy_builder_handler.handle_duplicatestrategy))
            self.application.add_handler(CommandHandler("exportstrategy", self.strategy_builder_handler.handle_exportstrategy))
            self.application.add_handler(CommandHandler("importstrategy", self.strategy_builder_handler.handle_importstrategy))
            self.application.add_handler(CommandHandler("searchstrategies", self.strategy_builder_handler.handle_searchstrategies))
        else:
            print("   ⚠️ Strategy builder handler not available")



        # Message handlers for wizard steps
        print("   - Adding message handler")
        # Remove chat ID filtering to allow all users
        self.application.add_handler(MessageHandler(
            filters.TEXT & ~filters.COMMAND,
            self.handle_message
        ))

        # Callback query handler
        print("   - Adding callback query handler")
        self.application.add_handler(CallbackQueryHandler(self.handle_callback_query))

        # Add debug handler for all updates
        print("   - Adding debug update handler")
        self.application.add_handler(MessageHandler(filters.ALL, self.debug_update_handler), group=999)
    
    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        # This method is required by TelegramBaseHandler but not used in our architecture
        # Commands are handled by specific handlers registered in _setup_handlers
        pass

    @require_auth("USER")
    async def handle_start(self, update: Update, context) -> None:
        """Handle /start command with enhanced welcome message"""
        self.logger.info(f"handle_start called by user {update.effective_user.id}")

        # Track user interaction for username resolution
        await self._track_user_interaction(update.effective_user)

        try:
            # Use enhanced welcome template
            self.logger.info("Getting welcome message template")
            template = TelegramTemplates.welcome_message()

            # Create inline keyboard
            self.logger.info("Creating inline keyboard")
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            self.logger.info("Sending welcome message")
            await self._send_safe_message(
                update.message.reply_text,
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )
            self.logger.info("Welcome message sent successfully")

        except Exception as e:
            self.logger.error(f"Error in handle_start: {e}", exc_info=True)
            # Fallback to simple message
            try:
                await update.message.reply_text(
                    "🤖 **Welcome to AutoTrader Bot!**\n\n"
                    "Use /help to see available commands.",
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e2:
                self.logger.error(f"Error in fallback message: {e2}", exc_info=True)
    
    @require_auth("USER")
    async def handle_help(self, update: Update, context) -> None:
        """Handle /help command with enhanced help system"""
        self.logger.info(f"handle_help called by user {update.effective_user.id}")
        try:
            # Use enhanced help template
            self.logger.info("Getting help template")
            template = TelegramTemplates.help_main()

            # Create inline keyboard
            self.logger.info("Creating help keyboard")
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            self.logger.info("Sending help message")
            self.logger.info(f"Template content length: {len(template.content)}")
            self.logger.info(f"Template content preview: {template.content[:200]}...")

            await self._send_safe_message(
                update.message.reply_text,
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )
            self.logger.info("Help message sent successfully")

        except Exception as e:
            self.logger.error(f"Error in handle_help: {e}", exc_info=True)
            # Fallback to simple help
            try:
                await update.message.reply_text(
                    "📚 **Help**\n\n"
                    "Use /addcreds to add credentials\n"
                    "Use /assets <profile> to view account assets\n"
                    "Use /createbot to create a trading bot\n"
                    "Use /list to see all bots",
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e2:
                self.logger.error(f"Error in fallback help: {e2}", exc_info=True)

    async def handle_test(self, update: Update, context) -> None:
        """Simple test handler to verify bot is working"""
        self.logger.info(f"handle_test called by user {update.effective_user.id}")
        try:
            await update.message.reply_text(
                "✅ **Test Successful!**\n\n"
                f"User ID: {update.effective_user.id}\n"
                f"Chat ID: {update.effective_chat.id}\n"
                f"Message: {update.message.text}",
                parse_mode=ParseMode.MARKDOWN
            )
            self.logger.info("Test message sent successfully")
        except Exception as e:
            self.logger.error(f"Error in handle_test: {e}", exc_info=True)

    async def handle_selftest(self, update: Update, context) -> None:
        """Handle /selftest command - comprehensive testing interface"""
        self.logger.info(f"handle_selftest called by user {update.effective_user.id}")

        try:
            if self.self_test_handler:
                await self.self_test_handler.handle_selftest(update, context)
            else:
                # Fallback to basic self test
                await self.safe_sender.send_safe_message(
                    update.message.reply_text,
                    "🤖 **Self-Test Mode Activated**\n\nBot sẽ tự gửi và xử lý các commands...",
                    parse_mode=ParseMode.MARKDOWN
                )

            # Commands to test
            test_commands = [
                "/help",
                "/createbot",
                "/addcreds",
                "/list",
                "/status"
            ]

            for i, cmd in enumerate(test_commands, 1):
                await asyncio.sleep(2)  # Delay between tests

                # Notify about current test
                await self.safe_sender.send_safe_message(
                    update.message.reply_text,
                    f"🔄 **Self-Test {i}/5**: Testing `{cmd}`...",
                    parse_mode=ParseMode.MARKDOWN
                )

                # Actually simulate and process the command
                await self.simulate_user_message(
                    chat_id=update.effective_chat.id,
                    message_text=cmd,
                    user_id=999999999  # Fake user ID
                )

                await asyncio.sleep(1)  # Small delay after processing

            # Final summary
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                "✅ **Self-Test Complete**\n\nTất cả commands đã được test! Kiểm tra logs để xem kết quả.",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in handle_selftest: {e}", exc_info=True)

    async def handle_autotest(self, update: Update, context) -> None:
        """Handle /autotest command - fully automated testing"""
        self.logger.info(f"handle_autotest called by user {update.effective_user.id}")

        try:
            if self.auto_test_handler:
                await self.auto_test_handler.handle_autotest(update, context)
            else:
                await self.safe_sender.send_safe_message(
                    update.message.reply_text,
                    "❌ **Auto Test Handler Not Available**\n\n"
                    "Auto test handler is not initialized.",
                    parse_mode=ParseMode.MARKDOWN
                )
        except Exception as e:
            self.logger.error(f"Error in handle_autotest: {e}")
            await update.message.reply_text(f"❌ Lỗi: {str(e)}")

    async def simulate_user_message(self, chat_id: int, message_text: str, user_id: int = None):
        """Simulate a user message and actually process it through handlers"""
        if user_id is None:
            user_id = chat_id

        try:
            from telegram import Update, Message, User, Chat
            from telegram.constants import ChatType
            import time

            # Log simulation start
            print(f"🔄 SIMULATED: Creating fake message from user {user_id}")
            print(f"   Message: '{message_text}'")
            print(f"   Chat ID: {chat_id}")
            self.logger.info(f"🔄 SIMULATED: Message from user {user_id}: {message_text}")

            # Create fake User object
            fake_user = User(
                id=user_id,
                is_bot=False,
                first_name="SimulatedUser",
                username="simulated_user"
            )

            # Create fake Chat object
            fake_chat = Chat(
                id=chat_id,
                type=ChatType.PRIVATE
            )

            # Create fake Message object
            fake_message = Message(
                message_id=int(time.time()),
                date=int(time.time()),
                chat=fake_chat,
                from_user=fake_user,
                text=message_text
            )

            # Create fake Update object
            fake_update = Update(
                update_id=int(time.time()),
                message=fake_message
            )

            # Process the fake update through the application's handlers
            print(f"🔄 SIMULATED: Processing fake update through handlers...")
            self.logger.info(f"🔄 SIMULATED: Processing fake update through handlers...")

            # Process the update
            await self.application.process_update(fake_update)

            print(f"✅ SIMULATED: Message processing complete")
            self.logger.info(f"✅ SIMULATED: Message processing complete")

        except Exception as e:
            print(f"❌ SIMULATED: Error processing fake message: {e}")
            self.logger.error(f"❌ SIMULATED: Error processing fake message: {e}", exc_info=True)

    async def handle_simulate(self, update: Update, context) -> None:
        """Handle /simulate command - simulate user messages"""
        self.logger.info(f"handle_simulate called by user {update.effective_user.id}")

        try:
            # Get message to simulate from command args
            if context.args:
                message_to_simulate = ' '.join(context.args)
            else:
                message_to_simulate = "/help"  # Default

            # Send response about what we're simulating
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                f"🎭 **Simulation Mode**\n\nSimulating message: `{message_to_simulate}`\n\nCheck logs for simulation details...",
                parse_mode=ParseMode.MARKDOWN
            )

            # Simulate the message
            await self.simulate_user_message(
                chat_id=update.effective_chat.id,
                message_text=message_to_simulate,
                user_id=999999999  # Fake user ID for simulation
            )

            # Send completion message
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                f"✅ **Simulation Complete**\n\nMessage `{message_to_simulate}` has been simulated and logged!",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in handle_simulate: {e}", exc_info=True)

    async def handle_testcmd(self, update: Update, context) -> None:
        """Handle /testcmd command - test a specific command"""
        self.logger.info(f"handle_testcmd called by user {update.effective_user.id}")

        try:
            # Get command to test from args
            if not context.args:
                await self.safe_sender.send_safe_message(
                    update.message.reply_text,
                    "❌ **Usage**: `/testcmd <command>`\n\n**Examples:**\n• `/testcmd /help`\n• `/testcmd /createbot`\n• `/testcmd /list`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            command_to_test = ' '.join(context.args)

            # Send notification
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                f"🧪 **Testing Command**: `{command_to_test}`\n\nProcessing...",
                parse_mode=ParseMode.MARKDOWN
            )

            # Actually simulate and process the command
            await self.simulate_user_message(
                chat_id=update.effective_chat.id,
                message_text=command_to_test,
                user_id=888888888  # Different fake user ID for testcmd
            )

            # Send completion message
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                f"✅ **Test Complete**: `{command_to_test}`\n\nKiểm tra logs và messages phía trên để xem kết quả!",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in handle_testcmd: {e}", exc_info=True)

    async def debug_update_handler(self, update: Update, context) -> None:
        """Debug handler to log all updates"""
        print(f"🔍 DEBUG: Received update: {update.update_id}")
        print(f"   Update type: {type(update)}")
        print(f"   Update dict: {update.to_dict()}")

        if update.message:
            print(f"   Message from user {update.effective_user.id}: {update.message.text}")
            print(f"   Chat ID: {update.effective_chat.id}")
            print(f"   Message ID: {update.message.message_id}")
        if update.callback_query:
            print(f"   Callback query from user {update.effective_user.id}: {update.callback_query.data}")

        self.logger.info(f"Debug update: {update.update_id} from user {update.effective_user.id}")

        # Always continue processing
        return
    
    async def handle_message(self, update: Update, context) -> None:
        """Handle text messages (wizard steps)"""
        user_id = update.effective_user.id

        # Track user interaction for username resolution
        await self._track_user_interaction(update.effective_user)

        # Debug logging
        is_in_wizard = self._is_in_wizard(user_id)
        current_step = self._get_wizard_step(user_id)
        message_text = update.message.text
        print(f"🔍 DEBUG: Message handler - User {user_id}: message='{message_text}', in_wizard={is_in_wizard}, step={current_step}")
        print(f"🔍 DEBUG: Session manager ID: {id(self.session_manager)}")
        print(f"🔍 DEBUG: Session data: {self.session_manager.sessions.get(user_id, 'No session')}")
        self.logger.info(f"🔍 Message handler - User {user_id}: in_wizard={is_in_wizard}, step={current_step}")

        if not is_in_wizard:
            # Try strategy builder handler for text input
            if self.strategy_builder_handler:
                await self.strategy_builder_handler.handle_message_input(update, context)
            else:
                await self._send_info_message(
                    update,
                    "Sử dụng `/help` để xem danh sách lệnh có sẵn"
                )
            return

        # Get current wizard step
        step = current_step

        # Route to appropriate handler based on wizard type
        if step and step.startswith('addcreds_'):
            self.logger.info(f"🔄 Routing to credential handler for step: {step}")
            await self.credential_handler.handle_addcreds_wizard_step(update, context)
        elif step and step.startswith('createbot_'):
            self.logger.info(f"🔄 Routing to wizard handler for step: {step}")
            await self.wizard_handler.handle_createbot_wizard_step(update, context)
        else:
            self.logger.warning(f"❌ Invalid wizard step: {step}")
            await self._send_error_message(update, "Wizard step không hợp lệ")
            self._clear_wizard(user_id)
    
    async def handle_callback_query(self, update: Update, context) -> None:
        """Handle callback queries from inline keyboards"""
        query = update.callback_query

        # Check authorization first
        user = query.from_user
        user_id = user.id if user else None
        username = user.username if user else None

        if user_id is None or not self.auth_service.is_authorized(user_id, "USER", username):
            rejection_msg = self.auth_service.get_rejection_message()
            await query.answer(rejection_msg, show_alert=True)
            return

        try:
            await query.answer()
            data = query.data

            # Validate callback data
            if not data or not isinstance(data, str):
                await query.edit_message_text("❌ Callback data không hợp lệ")
                return

            # Route callback to appropriate handler
            if data == "close":
                # Handle close button - delete the message
                await query.delete_message()
            elif data.startswith("help_"):
                await self._handle_help_callback(query, data)
            elif data.startswith("createbot_"):
                await self._handle_createbot_callback(query, data)
            elif data.startswith("test_") or data in ["test_menu", "test_all", "test_results", "clear_test_results"]:
                if self.self_test_handler:
                    if data.startswith("test_step_") or data.startswith("test_builder_") or data.startswith("test_my_strategies"):
                        # These are test callbacks that should show test results
                        await self.self_test_handler.handle_test_callback_result(query, data)
                    elif data.startswith("simulate_"):
                        await self.self_test_handler.handle_simulate_command(query, data)
                    else:
                        await self.self_test_handler.handle_test_callback(query, data)
                else:
                    await query.edit_message_text("❌ Self test handler not available")
            elif data.startswith("auto_") or data in ["auto_menu", "auto_full_test", "auto_results", "auto_stop"]:
                if self.auto_test_handler:
                    await self.auto_test_handler.handle_autotest_callback(query, data)
                else:
                    await query.edit_message_text("❌ Auto test handler not available")
            elif (data.startswith("strategy_") and (data.startswith("strategy_conservative") or data.startswith("strategy_aggressive") or data.startswith("strategy_balanced") or data.startswith("strategy_scalping") or data.startswith("strategy_swing") or data.startswith("strategy_custom"))) or data == "create_new_strategy" or data == "strategy_compare":
                # Route strategy selection callbacks from createbot to wizard handler
                self.logger.info(f"🔍 Routing createbot strategy callback to wizard handler: data='{data}'")
                await self._handle_createbot_callback(query, data)
            elif data.startswith("builder_") or data.startswith("template_") or data == "my_strategies" or data.startswith("delete_strategy_") or data.startswith("confirm_delete_") or data.startswith("step_") or data.startswith("entry_") or data.startswith("dca_") or data.startswith("exit_") or data.startswith("tp_") or data.startswith("sl_"):
                self.logger.info(f"🔍 Routing to builder callback: data='{data}'")
                await self._handle_builder_callback(query, data)
            elif data.startswith("startbot_"):
                await self._handle_startbot_callback(query, data)
            elif data.startswith("stop_"):
                await self._handle_stop_callback(query, data)
            elif data.startswith("delete_creds_") or data.startswith("creds_confirm_delete_"):
                await self._handle_delete_creds_callback(query, data)
            elif data.startswith("stopall_"):
                await self._handle_stopall_callback(query, data)
            elif data.startswith("admin_"):
                await self._handle_admin_callback(query, data)
            elif data.startswith("bot_") or data.startswith("creds_"):
                await self._handle_generic_callback(query, data)
            elif data.startswith("wizard_"):
                await self._handle_wizard_callback(query, data)
            elif data.startswith("profile_"):
                await self._handle_profile_callback(query, data)
            elif data.startswith("restart_all_"):
                await self._handle_restart_all_callback(query, data)
            elif data.startswith("remove_confirm_"):
                await self._handle_remove_confirm_callback(query, data)
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")

        except Exception as e:
            self.logger.error(f"Error handling callback query: {e}")
            try:
                await query.edit_message_text("❌ Lỗi xử lý callback")
            except Exception:
                # If edit fails, try to answer the callback at least
                try:
                    await query.answer("❌ Lỗi xử lý callback", show_alert=True)
                except Exception:
                    pass  # Give up completely
    
    async def _handle_help_callback(self, query, data: str) -> None:
        """Handle help-related callbacks with enhanced templates"""
        try:
            self.logger.info(f"Processing help callback: {data}")
            template = None

            if data == "help_main":
                template = TelegramTemplates.help_main()
            elif data == "help_credentials":
                template = TelegramTemplates.help_credentials()
            elif data == "help_bots":
                template = TelegramTemplates.help_bots()
            elif data == "help_monitoring":
                template = TelegramTemplates.help_monitoring()
            elif data == "help_getting_started":
                template = TelegramTemplates.help_getting_started()
            elif data == "help_admin":
                self.logger.info("Getting admin help template")
                template = TelegramTemplates.help_admin()
                self.logger.info(f"Admin template content length: {len(template.content)}")
            elif data == "help_faq":
                template = TelegramTemplates.help_faq()
            else:
                await query.edit_message_text(
                    "❌ Tùy chọn không hợp lệ",
                    parse_mode=ParseMode.HTML
                )
                return

            if template:
                self.logger.info(f"Creating keyboard for template: {template.title}")
                keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                self.logger.info("Sending template message")
                await query.edit_message_text(
                    template.content,
                    parse_mode=ParseMode.HTML,
                    reply_markup=keyboard
                )
                self.logger.info("Template message sent successfully")

        except Exception as e:
            self.logger.error(f"Error in help callback: {e}", exc_info=True)
            await query.edit_message_text(
                "❌ Có lỗi xảy ra khi hiển thị help",
                parse_mode=ParseMode.HTML
            )

    async def _handle_stopall_callback(self, query, data: str) -> None:
        """Handle stopall confirmation callbacks"""
        try:
            if data == "stopall_confirm":
                await self.bot_management_handler._execute_stop_all(query)
            elif data == "stopall_cancel":
                await query.edit_message_text(
                    "❌ **Stop All Cancelled**\n\nNo bots were stopped.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    "❌ Invalid stopall callback",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            self.logger.error(f"Error in stopall callback: {e}")
            await query.edit_message_text(
                "❌ Error processing stopall request",
                parse_mode=ParseMode.HTML
            )

    async def _handle_generic_callback(self, query, data: str) -> None:
        """Handle generic callbacks for bot and credential actions"""
        try:
            if data.startswith("bot_"):
                # Delegate bot-related callbacks to bot management handler
                if self.bot_management_handler:
                    if data == "bot_list":
                        await self.bot_management_handler.handle_list_callback(query)
                    elif data == "bot_start_all":
                        await self.bot_management_handler.handle_startall_callback(query)
                    elif data == "bot_stop_all":
                        await self.bot_management_handler.handle_stopall_callback(query, "stopall_confirm")
                    elif data == "bot_clean_stopped":
                        await self.bot_management_handler.handle_clean_stopped_callback(query)
                    elif data == "bot_create":
                        # Start create bot wizard
                        await query.edit_message_text(
                            "🚀 Starting bot creation wizard...\n\nUse /createbot command to begin.",
                            parse_mode=None
                        )
                    elif data == "bot_stats":
                        await query.edit_message_text(
                            "📊 Use /status command to view detailed statistics.",
                            parse_mode=None
                        )
                    elif data == "bot_create_quick":
                        await self._handle_bot_create_quick(query)
                    elif data == "bot_logs_quick":
                        await self._handle_bot_logs_quick(query)
                    elif data == "bot_status_all":
                        await self._handle_bot_status_all(query)
                    elif data == "bot_restart_all":
                        await self._handle_bot_restart_all(query)
                    elif data == "bot_help":
                        await self._handle_bot_help(query)
                    elif data == "bot_add_creds":
                        await self._handle_bot_add_creds(query)
                    elif data.startswith("bot_start_"):
                        container_name = data.replace("bot_start_", "")
                        await self._handle_individual_bot_action(query, "start", container_name)
                    elif data.startswith("bot_stop_"):
                        container_name = data.replace("bot_stop_", "")
                        await self._handle_individual_bot_action(query, "stop", container_name)
                    elif data.startswith("bot_restart_"):
                        container_name = data.replace("bot_restart_", "")
                        await self._handle_individual_bot_action(query, "restart", container_name)
                    elif data.startswith("bot_logs_"):
                        container_name = data.replace("bot_logs_", "")
                        await self._handle_individual_bot_action(query, "logs", container_name)
                    elif data.startswith("bot_remove_"):
                        container_name = data.replace("bot_remove_", "")
                        await self._handle_individual_bot_action(query, "remove", container_name)
                    elif data.startswith("bot_status_"):
                        container_name = data.replace("bot_status_", "")
                        await self._handle_individual_bot_action(query, "status", container_name)
                    else:
                        await query.edit_message_text(
                            "❌ Bot callback not implemented yet",
                            parse_mode=None
                        )
                else:
                    await query.edit_message_text(
                        "❌ Bot management handler không khả dụng",
                        parse_mode=None
                    )

            elif data.startswith("creds_"):
                # Credential-related callbacks
                if data == "creds_add":
                    await query.edit_message_text(
                        "🔑 **Starting Credential Wizard**\n\n"
                        "Please use the command: /addcreds\n\n"
                        "This will start the interactive credential setup wizard.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                elif data == "creds_list":
                    await query.edit_message_text(
                        "📋 **View Credentials**\n\n"
                        "Please use the command: /listcreds\n\n"
                        "This will show all your stored credential profiles.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                elif data == "creds_manage":
                    await self._handle_creds_manage(query)
                else:
                    await query.edit_message_text(
                        "❌ Credential callback not implemented yet",
                        parse_mode=ParseMode.HTML
                    )
            else:
                await query.edit_message_text(
                    "❌ Unknown callback",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            self.logger.error(f"Error in generic callback: {e}")
            await query.edit_message_text(
                "❌ Error processing callback",
                parse_mode=ParseMode.HTML
            )
    
    async def _handle_createbot_callback(self, query, data: str) -> None:
        """Handle createbot-related callbacks"""
        if data.startswith("createbot_profile_"):
            profile = data.replace("createbot_profile_", "")
            await self.wizard_handler.handle_createbot_profile_callback(query, profile)
        elif data.startswith("createbot_direction_"):
            direction = data.replace("createbot_direction_", "")
            await self.wizard_handler.handle_createbot_direction_callback(query, direction)
        elif data.startswith("createbot_testmode_"):
            test_mode = data.replace("createbot_testmode_", "")
            await self.wizard_handler.handle_createbot_testmode_callback(query, test_mode)
        elif data == "createbot_confirm":
            await self.wizard_handler.handle_createbot_confirm(query)
        elif data == "createbot_cancel":
            user_id = query.from_user.id
            self._clear_wizard(user_id)
            await query.edit_message_text("❌ **Đã hủy tạo bot**", parse_mode=ParseMode.MARKDOWN)
        else:
            # Pass to wizard handler for other createbot callbacks
            await self.wizard_handler.handle_createbot_callback(query, data)

    async def _handle_strategy_callback(self, query, data: str) -> None:
        """Handle strategy-related callbacks"""
        try:
            if self.bot_management_handler:
                await self.bot_management_handler.handle_strategy_callback(query, data)
            else:
                await query.edit_message_text("❌ Bot management handler không khả dụng")
        except Exception as e:
            self.logger.error(f"Error in strategy callback: {e}")
            await query.edit_message_text("❌ Lỗi xử lý strategy callback")

    async def _handle_builder_callback(self, query, data: str) -> None:
        """Handle strategy builder callbacks"""
        try:
            if self.strategy_builder_handler:
                await self.strategy_builder_handler.handle_builder_callback(query, data)
            else:
                await query.edit_message_text("❌ Strategy builder handler không khả dụng")
        except Exception as e:
            self.logger.error(f"Error in builder callback: {e}")
            await query.edit_message_text("❌ Lỗi xử lý builder callback")

    async def _handle_startbot_callback(self, query, data: str) -> None:
        """Handle startbot-related callbacks"""
        await self.bot_management_handler.handle_startbot_callback(query, data)

    async def _handle_stop_callback(self, query, data: str) -> None:
        """Handle stop-related callbacks"""
        if data.startswith("stop_confirm_"):
            symbol = data.replace("stop_confirm_", "")
            await self.bot_management_handler.handle_stop_confirm(query, symbol)
        elif data == "stop_cancel":
            await query.edit_message_text("❌ **Đã hủy dừng bot**", parse_mode=ParseMode.MARKDOWN)
    
    async def _handle_delete_creds_callback(self, query, data: str) -> None:
        """Handle credential deletion callbacks"""
        # Pass all delete_creds_ callbacks to credential handler
        await self.credential_handler.handle_delete_creds_callback(query, data)

    async def _send_unauthorized_message(self, update: Update) -> None:
        """Send unauthorized access message to user"""
        try:
            rejection_msg = self.auth_service.get_rejection_message()

            # Add user info for admin reference
            user = update.effective_user
            user_info = f"\n\n🔍 **User Info:**\n" \
                       f"• ID: `{user.id}`\n" \
                       f"• Username: @{user.username or 'N/A'}\n" \
                       f"• Name: {user.first_name or ''} {user.last_name or ''}".strip()

            full_message = rejection_msg + user_info

            if update.message:
                await update.message.reply_text(full_message, parse_mode=ParseMode.MARKDOWN)
            elif update.callback_query:
                await update.callback_query.answer(rejection_msg, show_alert=True)

        except Exception as e:
            self.logger.error(f"Error sending unauthorized message: {e}")

    # User Management Commands (Admin Only)

    @require_auth("SUPER_ADMIN")
    async def handle_add_user(self, update: Update, context) -> None:
        """Handle /adduser command - add user to authorized list (SUPER_ADMIN only)"""
        if len(context.args) < 2:
            await self._send_error_message(
                update,
                "**Sử dụng:** `/adduser <user_id_or_username> <role> [display_name]`\n\n"
                "**Roles:** USER, ADMIN\n"
                "**Ví dụ:** \n"
                "- `/adduser 123456789 USER john_doe`\n"
                "- `/adduser @john_doe USER`\n"
                "- `/adduser john_doe USER John Doe`"
            )
            return

        try:
            user_identifier = context.args[0]
            role = context.args[1].upper()
            display_name = context.args[2] if len(context.args) > 2 else None
            added_by = update.effective_user.id

            # Resolve user ID and username
            user_id, username = await self._resolve_user_identifier(user_identifier, context)

            if user_id is None:
                await self._send_error_message(
                    update,
                    f"❌ Không thể tìm thấy user: {user_identifier}\n"
                    "Hãy đảm bảo username đúng hoặc user đã từng tương tác với bot."
                )
                return

            # Use display_name if provided, otherwise use resolved username
            final_username = display_name or username or f"user_{user_id}"

            success, message = self.auth_service.add_user(user_id, final_username, role, added_by)

            if success:
                await self._send_success_message(update, message)
            else:
                await self._send_error_message(update, message)

        except Exception as e:
            self.logger.error(f"Error in handle_add_user: {e}")
            await self._send_error_message(update, f"❌ Lỗi: {str(e)}")

    async def _resolve_user_identifier(self, identifier: str, context) -> tuple[int, str]:
        """
        Resolve user identifier (username or user_id) to user_id and username.

        Args:
            identifier: Either a user ID (number) or username (with or without @)
            context: Telegram context for API calls

        Returns:
            tuple: (user_id, username) or (None, None) if not found
        """
        try:
            # Case 1: It's a numeric user ID
            if identifier.isdigit():
                user_id = int(identifier)
                try:
                    # Try to get user info from Telegram
                    chat_member = await context.bot.get_chat_member(user_id, user_id)
                    username = chat_member.user.username or chat_member.user.first_name or f"user_{user_id}"
                    return user_id, username
                except Exception:
                    # If we can't get info from Telegram, still return the user_id
                    return user_id, f"user_{user_id}"

            # Case 2: It's a username (with or without @)
            username = identifier.lstrip('@')

            # Try to resolve username through recent bot interactions
            # This is a limitation - we can only resolve users who have interacted with the bot
            user_id = await self._find_user_id_by_username(username)

            if user_id:
                return user_id, username

            # If we can't resolve, return None
            return None, None

        except Exception as e:
            self.logger.error(f"Error resolving user identifier {identifier}: {e}")
            return None, None

    async def _find_user_id_by_username(self, username: str) -> int:
        """
        Find user ID by username from bot's interaction history.
        This is a workaround since Telegram Bot API doesn't allow username->ID resolution
        without the user interacting with the bot first.
        """
        try:
            # Method 1: Check if we have this user in our authorized users list
            users = self.auth_service.list_users()
            for user in users:
                stored_username = user.get('username', '').lower()
                # Check both exact match and without user_ prefix
                if (stored_username == username.lower() or
                    stored_username == f"user_{username}".lower() or
                    stored_username.replace('user_', '') == username.lower()):
                    return user['user_id']

            # Method 2: Check recent interactions cache (if implemented)
            # This would require storing user interactions in a cache/database
            user_id = await self._check_user_interactions_cache(username)
            if user_id:
                return user_id

            return None

        except Exception as e:
            self.logger.error(f"Error finding user ID for username {username}: {e}")
            return None

    async def _check_user_interactions_cache(self, username: str) -> int:
        """
        Check user interactions cache for username->user_id mapping.
        """
        try:
            # Check in-memory cache
            if hasattr(self, '_user_cache'):
                for cached_username, user_id in self._user_cache.items():
                    if cached_username.lower() == username.lower():
                        return user_id
            return None
        except Exception as e:
            self.logger.error(f"Error checking user cache: {e}")
            return None

    async def _track_user_interaction(self, user) -> None:
        """
        Track user interaction for username resolution.
        Store username->user_id mapping in memory cache.
        """
        try:
            # Initialize cache if not exists
            if not hasattr(self, '_user_cache'):
                self._user_cache = {}

            # Store username if available
            if user.username:
                self._user_cache[user.username.lower()] = user.id
                self.logger.debug(f"Cached user interaction: @{user.username} -> {user.id}")

            # Also store first_name as fallback
            if user.first_name:
                self._user_cache[user.first_name.lower()] = user.id
                self.logger.debug(f"Cached user interaction: {user.first_name} -> {user.id}")

        except Exception as e:
            self.logger.error(f"Error tracking user interaction: {e}")

    # Bot Management Callback Handlers

    async def _handle_bot_create_quick(self, query) -> None:
        """Handle quick bot creation callback"""
        try:
            await query.edit_message_text(
                "🚀 **Quick Bot Creation**\n\n"
                "Use the command: `/createbot`\n\n"
                "This will start the interactive bot creation wizard with:\n"
                "• Profile selection\n"
                "• Symbol input\n"
                "• Amount configuration\n"
                "• Test/Live mode selection",
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Error in bot_create_quick callback: {e}")
            await query.edit_message_text("❌ Error starting bot creation wizard")

    async def _handle_bot_logs_quick(self, query) -> None:
        """Handle quick logs view callback"""
        try:
            # Get running containers for quick logs access
            containers = await self.bot_management_handler.container_helper.list_containers()
            running_containers = [c for c in containers if c['status'] == 'running']

            if not running_containers:
                await query.edit_message_text(
                    "📋 **No Running Bots**\n\n"
                    "No bots are currently running.\n"
                    "Start a bot first using `/createbot` or `/list`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            if len(running_containers) == 1:
                # Single container - show logs directly
                container_name = running_containers[0]['name']
                await self.bot_management_handler.handle_logs_command(query, container_name, 50)
            else:
                # Multiple containers - show selection
                message = "📋 **Quick Logs Access**\n\nSelect a bot to view logs:\n\n"

                keyboard = []
                for container in running_containers[:10]:  # Limit to 10 for UI
                    status_emoji = "🟢" if container['status'] == 'running' else "🔴"
                    keyboard.append([{
                        "text": f"{status_emoji} {container['name']}",
                        "callback_data": f"bot_logs_{container['name']}"
                    }])

                keyboard.append([{"text": "❌ Cancel", "callback_data": "close"}])

                reply_markup = self._create_inline_keyboard(keyboard)
                await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN, reply_markup=reply_markup)

        except Exception as e:
            self.logger.error(f"Error in bot_logs_quick callback: {e}")
            await query.edit_message_text("❌ Error accessing logs")

    async def _handle_bot_status_all(self, query) -> None:
        """Handle status all bots callback"""
        try:
            # Delegate to bot management handler
            await self.bot_management_handler.handle_status_all_callback(query)
        except Exception as e:
            self.logger.error(f"Error in bot_status_all callback: {e}")
            await query.edit_message_text("❌ Error getting bot status")

    async def _handle_bot_restart_all(self, query) -> None:
        """Handle restart all bots callback"""
        try:
            # Show confirmation dialog
            containers = await self.bot_management_handler.container_helper.list_containers()
            running_containers = [c for c in containers if c['status'] == 'running']

            if not running_containers:
                await query.edit_message_text(
                    "🔄 **No Running Bots**\n\n"
                    "No bots are currently running to restart.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            message = f"🔄 **Restart All Bots**\n\n"
            message += f"This will restart {len(running_containers)} running bot(s):\n\n"

            for container in running_containers[:5]:  # Show first 5
                message += f"• {container['name']}\n"

            if len(running_containers) > 5:
                message += f"• ... and {len(running_containers) - 5} more\n"

            message += "\n⚠️ **Warning:** This will temporarily stop all bots during restart.\n\n"
            message += "Are you sure you want to continue?"

            keyboard = [
                [{"text": "✅ Yes, Restart All", "callback_data": "restart_all_confirm"},
                 {"text": "❌ Cancel", "callback_data": "restart_all_cancel"}]
            ]

            reply_markup = self._create_inline_keyboard(keyboard)
            await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN, reply_markup=reply_markup)

        except Exception as e:
            self.logger.error(f"Error in bot_restart_all callback: {e}")
            await query.edit_message_text("❌ Error preparing restart all")

    async def _handle_bot_help(self, query) -> None:
        """Handle bot help callback"""
        try:
            template = TelegramTemplates.help_bots()
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
            await query.edit_message_text(
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )
        except Exception as e:
            self.logger.error(f"Error in bot_help callback: {e}")
            await query.edit_message_text("❌ Error showing bot help")

    async def _handle_bot_add_creds(self, query) -> None:
        """Handle add credentials from bot interface callback"""
        try:
            await query.edit_message_text(
                "🔑 **Add Trading Credentials**\n\n"
                "Use the command: `/addcreds`\n\n"
                "This will start the credential setup wizard where you can:\n"
                "• Add new trading account credentials\n"
                "• Set profile names for easy management\n"
                "• Test connection to exchange\n\n"
                "💡 **Tip:** You can manage multiple profiles for different accounts!",
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Error in bot_add_creds callback: {e}")
            await query.edit_message_text("❌ Error showing credentials help")

    async def _handle_individual_bot_action(self, query, action: str, container_name: str) -> None:
        """Handle individual bot actions (start, stop, restart, logs, remove, status)"""
        try:
            if action == "start":
                await self.bot_management_handler.handle_start_individual(query, container_name)
            elif action == "stop":
                await self.bot_management_handler.handle_stop_individual(query, container_name)
            elif action == "restart":
                await self.bot_management_handler.handle_restart_individual(query, container_name)
            elif action == "logs":
                await self.bot_management_handler.handle_logs_individual(query, container_name)
            elif action == "remove":
                await self.bot_management_handler.handle_remove_individual(query, container_name)
            elif action == "status":
                await self.bot_management_handler.handle_status_individual(query, container_name)
            else:
                await query.edit_message_text(f"❌ Unknown action: {action}")

        except Exception as e:
            self.logger.error(f"Error in individual bot action {action} for {container_name}: {e}")
            await query.edit_message_text(f"❌ Error performing {action} on {container_name}")

    async def _handle_creds_manage(self, query) -> None:
        """Handle credentials management callback"""
        try:
            await query.edit_message_text(
                "🔧 **Credential Management**\n\n"
                "Available actions:\n\n"
                "📋 **View:** `/listcreds` - List all profiles\n"
                "➕ **Add:** `/addcreds` - Add new credentials\n"
                "🗑️ **Delete:** `/deletecreds` - Remove credentials\n"
                "💰 **Assets:** `/assets <profile>` - View account balance\n\n"
                "💡 **Tip:** Use profile names to organize multiple trading accounts!",
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Error in creds_manage callback: {e}")
            await query.edit_message_text("❌ Error showing credential management")

    # Wizard Callback Handlers

    async def _handle_wizard_callback(self, query, data: str) -> None:
        """Handle wizard-related callbacks"""
        try:
            user_id = query.from_user.id

            if data == "wizard_skip":
                # Skip current wizard step
                await query.edit_message_text(
                    "⏭️ **Step Skipped**\n\n"
                    "Moving to next step...\n\n"
                    "Use `/createbot` to restart the wizard if needed.",
                    parse_mode=ParseMode.MARKDOWN
                )
                self._clear_wizard(user_id)

            elif data == "wizard_finish":
                # Finish wizard with current settings
                if self.wizard_handler:
                    await self.wizard_handler.handle_wizard_finish(query)
                else:
                    await query.edit_message_text("❌ Wizard handler not available")

            elif data == "wizard_cancel":
                # Cancel wizard
                await query.edit_message_text(
                    "❌ **Wizard Cancelled**\n\n"
                    "Bot creation has been cancelled.\n\n"
                    "Use `/createbot` to start again.",
                    parse_mode=ParseMode.MARKDOWN
                )
                self._clear_wizard(user_id)

            else:
                await query.edit_message_text("❌ Unknown wizard callback")

        except Exception as e:
            self.logger.error(f"Error in wizard callback {data}: {e}")
            await query.edit_message_text("❌ Error processing wizard action")

    # Profile Callback Handlers

    async def _handle_profile_callback(self, query, data: str) -> None:
        """Handle profile-related callbacks"""
        try:
            if data == "profile_stats":
                await self._handle_profile_stats(query)
            elif data == "profile_refresh":
                await self._handle_profile_refresh(query)
            elif data == "profile_compare":
                await self._handle_profile_compare(query)
            elif data == "profile_export":
                await self._handle_profile_export(query)
            elif data == "profile_help":
                await self._handle_profile_help(query)
            else:
                await query.edit_message_text("❌ Unknown profile callback")

        except Exception as e:
            self.logger.error(f"Error in profile callback {data}: {e}")
            await query.edit_message_text("❌ Error processing profile action")

    async def _handle_profile_stats(self, query) -> None:
        """Handle profile statistics callback"""
        try:
            # Get profile statistics
            profiles = await self.credential_handler.get_profiles()
            containers = await self.bot_management_handler.container_helper.list_containers()

            message = "📊 **Profile Statistics**\n\n"

            if not profiles:
                message += "No profiles found.\n\nUse `/addcreds` to add your first profile."
            else:
                for profile in profiles:
                    profile_name = profile.get('profile', 'Unknown')
                    display_name = profile.get('display_name', profile_name)

                    # Count bots for this profile
                    profile_containers = [c for c in containers if c['name'].startswith(f"{profile_name}-")]
                    running_count = len([c for c in profile_containers if c['status'] == 'running'])
                    total_count = len(profile_containers)

                    message += f"🔑 **{display_name}**\n"
                    message += f"   • Bots: {running_count}/{total_count} running\n"
                    message += f"   • Profile: `{profile_name}`\n\n"

            keyboard = [
                [{"text": "🔄 Refresh", "callback_data": "profile_refresh"},
                 {"text": "📋 View Profiles", "callback_data": "creds_list"}],
                [{"text": "❌ Close", "callback_data": "close"}]
            ]

            reply_markup = self._create_inline_keyboard(keyboard)
            await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN, reply_markup=reply_markup)

        except Exception as e:
            self.logger.error(f"Error in profile_stats: {e}")
            await query.edit_message_text("❌ Error loading profile statistics")

    async def _handle_profile_refresh(self, query) -> None:
        """Handle profile refresh callback"""
        try:
            # Refresh profiles data and show updated list
            await self.bot_management_handler.handle_profiles_callback(query)
        except Exception as e:
            self.logger.error(f"Error in profile_refresh: {e}")
            await query.edit_message_text("❌ Error refreshing profiles")

    async def _handle_profile_compare(self, query) -> None:
        """Handle profile comparison callback"""
        try:
            await query.edit_message_text(
                "🔀 **Profile Comparison**\n\n"
                "This feature allows you to compare performance across different profiles.\n\n"
                "📊 **Coming Soon:**\n"
                "• Performance metrics comparison\n"
                "• Profit/Loss analysis\n"
                "• Trading volume statistics\n\n"
                "💡 Use `/profiles` to view individual profile details for now.",
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Error in profile_compare: {e}")
            await query.edit_message_text("❌ Error showing profile comparison")

    async def _handle_profile_export(self, query) -> None:
        """Handle profile export callback"""
        try:
            await query.edit_message_text(
                "📤 **Export Profile Configuration**\n\n"
                "This feature will allow you to export your profile settings.\n\n"
                "🔧 **Coming Soon:**\n"
                "• Export trading configurations\n"
                "• Backup profile settings\n"
                "• Import/Export between instances\n\n"
                "💡 Use `/listcreds` to view current profiles.",
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Error in profile_export: {e}")
            await query.edit_message_text("❌ Error showing profile export")

    async def _handle_profile_help(self, query) -> None:
        """Handle profile help callback"""
        try:
            await query.edit_message_text(
                "📚 **Profile Management Guide**\n\n"
                "**What are Profiles?**\n"
                "Profiles let you manage multiple trading accounts with different credentials.\n\n"
                "**Commands:**\n"
                "• `/addcreds` - Add new profile\n"
                "• `/listcreds` - View all profiles\n"
                "• `/deletecreds` - Remove profile\n"
                "• `/assets <profile>` - Check balance\n\n"
                "**Creating Bots:**\n"
                "• `/createbot` - Select profile during creation\n"
                "• Bots are named: `profile-symbol` (e.g., `main-btcusdt`)\n\n"
                "💡 **Tip:** Use descriptive profile names like 'main', 'test', 'scalping'!",
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Error in profile_help: {e}")
            await query.edit_message_text("❌ Error showing profile help")

    # Restart All Callback Handler

    async def _handle_restart_all_callback(self, query, data: str) -> None:
        """Handle restart all confirmation callbacks"""
        try:
            if data == "restart_all_confirm":
                await self.bot_management_handler.handle_restart_all_confirm(query)
            elif data == "restart_all_cancel":
                await query.edit_message_text(
                    "❌ **Restart All Cancelled**\n\n"
                    "No bots were restarted.\n\n"
                    "Use `/list` to manage individual bots.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text("❌ Unknown restart all callback")

        except Exception as e:
            self.logger.error(f"Error in restart_all callback {data}: {e}")
            await query.edit_message_text("❌ Error processing restart all request")

    # Remove Confirmation Callback Handler

    async def _handle_remove_confirm_callback(self, query, data: str) -> None:
        """Handle remove confirmation callbacks"""
        try:
            container_name = data.replace("remove_confirm_", "")

            # Execute remove
            result = await self.bot_management_handler.unified_processor.process_remove_command(container_name, False)

            if result[0] == 0:
                await query.edit_message_text(
                    f"🗑️ **Bot Removed**\n\n`{container_name}` has been removed successfully.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Remove Failed**\n\n{result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )

        except Exception as e:
            self.logger.error(f"Error in remove_confirm callback: {e}")
            await query.edit_message_text("❌ Error removing bot")

    async def _handle_admin_callback(self, query, data: str) -> None:
        """Handle admin-related callbacks"""
        try:
            # Check if user is SUPER_ADMIN
            user_id = query.from_user.id
            if not self.auth_service._is_super_admin(user_id):
                await query.answer("❌ Chỉ SUPER_ADMIN mới có quyền truy cập", show_alert=True)
                return

            if data == "admin_list_users":
                # Show list of users
                users = self.auth_service.list_users()

                if not users:
                    content = "📋 **Danh sách người dùng được ủy quyền**\n\nKhông có người dùng nào được ủy quyền."
                else:
                    content = "📋 **Danh sách người dùng được ủy quyền**\n\n"
                    for user in users:
                        role_emoji = {"USER": "👤", "ADMIN": "👨‍💼", "SUPER_ADMIN": "👑"}.get(user['role'], "❓")
                        content += f"{role_emoji} **{user['username']}** ({user['user_id']})\n"
                        content += f"   Role: {user['role']}\n"
                        content += f"   Added: {user.get('added_at', 'Unknown')}\n\n"

                keyboard = [
                    [{"text": "🔄 Refresh", "callback_data": "admin_list_users"},
                     {"text": "📚 Back to Help", "callback_data": "help_admin"}],
                    [{"text": "❌ Close", "callback_data": "close"}]
                ]

                reply_markup = self._create_inline_keyboard(keyboard)
                await query.edit_message_text(
                    content,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )

            elif data == "admin_my_info":
                # Show current user info
                user_info = self.auth_service.get_user_info(user_id)

                if user_info:
                    role_emoji = {"USER": "👤", "ADMIN": "👨‍💼", "SUPER_ADMIN": "👑"}.get(user_info['role'], "❓")
                    content = f"ℹ️ **Thông tin của bạn**\n\n"
                    content += f"{role_emoji} **Username:** {user_info['username']}\n"
                    content += f"🆔 **User ID:** {user_id}\n"
                    content += f"🎭 **Role:** {user_info['role']}\n"
                    content += f"📅 **Added:** {user_info.get('added_at', 'Unknown')}\n"
                    content += f"👤 **Added by:** {user_info.get('added_by', 'Unknown')}"
                else:
                    content = "❌ Không tìm thấy thông tin người dùng"

                keyboard = [
                    [{"text": "📚 Back to Help", "callback_data": "help_admin"},
                     {"text": "❌ Close", "callback_data": "close"}]
                ]

                reply_markup = self._create_inline_keyboard(keyboard)
                await query.edit_message_text(
                    content,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )

            else:
                await query.answer("❌ Admin callback không hợp lệ", show_alert=True)

        except Exception as e:
            self.logger.error(f"Error in admin callback: {e}")
            await query.answer("❌ Lỗi xử lý admin callback", show_alert=True)

    @require_auth("SUPER_ADMIN")
    async def handle_remove_user(self, update: Update, context) -> None:
        """Handle /removeuser command - remove user from authorized list (SUPER_ADMIN only)"""
        if not context.args:
            await self._send_error_message(
                update,
                "**Sử dụng:** `/removeuser <user_id_or_username>`\n\n"
                "**Ví dụ:** \n"
                "- `/removeuser 123456789`\n"
                "- `/removeuser @john_doe`\n"
                "- `/removeuser john_doe`"
            )
            return

        try:
            user_identifier = context.args[0]
            removed_by = update.effective_user.id

            # Resolve user ID
            user_id, username = await self._resolve_user_identifier(user_identifier, context)

            if user_id is None:
                await self._send_error_message(
                    update,
                    f"❌ Không thể tìm thấy user: {user_identifier}\n"
                    "Hãy đảm bảo username đúng hoặc user có trong danh sách authorized."
                )
                return

            success, message = self.auth_service.remove_user(user_id, removed_by)

            if success:
                await self._send_success_message(update, message)
            else:
                await self._send_error_message(update, message)

        except Exception as e:
            self.logger.error(f"Error in handle_remove_user: {e}")
            await self._send_error_message(update, f"❌ Lỗi: {str(e)}")

    @require_auth("SUPER_ADMIN")
    async def handle_list_users(self, update: Update, context) -> None:
        """Handle /listusers command - list all authorized users (SUPER_ADMIN only)"""
        try:
            users = self.auth_service.list_users()

            if not users:
                await update.message.reply_text(
                    "📋 **Danh sách người dùng được ủy quyền**\n\n"
                    "Không có người dùng nào được ủy quyền.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            message = "👥 **Danh sách người dùng được ủy quyền**\n\n"

            for user in users:
                role_emoji = "👑" if user['role'] == "ADMIN" else "👤"
                message += f"{role_emoji} **{user['username']}**\n"
                message += f"   • ID: `{user['user_id']}`\n"
                message += f"   • Role: {user['role']}\n"
                message += f"   • Added by: {user['added_by']}\n"
                message += f"   • Added at: {user['added_at'][:10]}\n\n"

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error in handle_list_users: {e}")
            await self._send_error_message(update, f"❌ Lỗi: {str(e)}")

    @require_auth("USER")
    async def handle_my_info(self, update: Update, context) -> None:
        """Handle /myinfo command - show current user info"""
        try:
            user_id = update.effective_user.id
            user_info = self.auth_service.get_user_info(user_id)

            if not user_info:
                await self._send_error_message(update, "❌ Không tìm thấy thông tin người dùng")
                return

            role_emoji = "👑" if user_info['role'] == "ADMIN" else "👤"

            message = f"{role_emoji} **Thông tin của bạn**\n\n"
            message += f"• **Username:** {user_info.get('username', 'N/A')}\n"
            message += f"• **User ID:** `{user_id}`\n"
            message += f"• **Role:** {user_info['role']}\n"
            message += f"• **Added by:** {user_info.get('added_by', 'N/A')}\n"
            message += f"• **Added at:** {user_info.get('added_at', 'N/A')[:10]}\n\n"

            if user_info['role'] == "ADMIN":
                message += "🔧 **Admin Commands:**\n"
                message += "• `/adduser` - Thêm người dùng\n"
                message += "• `/removeuser` - Xóa người dùng\n"
                message += "• `/listusers` - Danh sách người dùng\n\n"

            message += "📱 **Available Commands:**\n"
            message += "• `/start` - Khởi động bot\n"
            message += "• `/help` - Trợ giúp\n"
            message += "• `/list` - Danh sách bots\n"
            message += "• `/startbot` - Tạo trading bot\n"
            message += "• `/addcreds` - Thêm credentials\n"
            message += "• `/assets` - Xem assets tài khoản"

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error in handle_my_info: {e}")
            await self._send_error_message(update, f"❌ Lỗi: {str(e)}")



# For backward compatibility
ImprovedTelegramCommandHandler = MainTelegramHandler
